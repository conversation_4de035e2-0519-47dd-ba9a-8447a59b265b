import api from '@/utils/api'

export interface SendVerificationCodeParams {
  email: string
  type?: 'register' | 'reset_password'
}

export interface VerifyCodeParams {
  email: string
  code: string
  type?: 'register' | 'reset_password'
}

export interface CodeStatusResponse {
  has_code: boolean
  is_valid?: boolean
  expires_at?: string
  time_left?: number
  created_at?: string
  message: string
}

// 发送邮箱验证码
export function sendVerificationCode(data: SendVerificationCodeParams) {
  return api.post('/email-verification/send', data)
}

// 验证邮箱验证码
export function verifyCode(data: VerifyCodeParams) {
  return api.post('/email-verification/verify', data)
}

// 检查验证码状态
export function checkCodeStatus(email: string, type: string = 'register') {
  return api.get<any, { data: CodeStatusResponse }>('/email-verification/status', {
    params: { email, type }
  })
}
