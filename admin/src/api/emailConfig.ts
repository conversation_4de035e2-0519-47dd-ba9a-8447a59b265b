import request from '@/utils/request'

export interface EmailConfig {
  id: number
  smtp_host: string
  smtp_port: number
  smtp_secure: boolean
  smtp_user: string
  from_name: string
  from_email: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface CreateEmailConfigParams {
  smtp_host: string
  smtp_port: number
  smtp_secure: boolean
  smtp_user: string
  smtp_pass: string
  from_name: string
  from_email: string
}

export interface UpdateEmailConfigParams {
  smtp_host?: string
  smtp_port?: number
  smtp_secure?: boolean
  smtp_user?: string
  smtp_pass?: string
  from_name?: string
  from_email?: string
}

export interface TestEmailConfigParams {
  test_email?: string
}

// 获取所有邮箱配置
export function getEmailConfigs(params?: { page?: number; limit?: number }) {
  return request.get<any, EmailConfig[]>('/admin/email-config', { params })
}

// 获取当前激活的邮箱配置
export function getActiveEmailConfig() {
  return request.get<any, { data: EmailConfig }>('/admin/email-config/active')
}

// 根据ID获取邮箱配置
export function getEmailConfigById(id: number) {
  return request.get<any, { data: EmailConfig }>(`/admin/email-config/${id}`)
}

// 创建邮箱配置
export function createEmailConfig(data: CreateEmailConfigParams) {
  return request.post<any, { data: EmailConfig }>('/admin/email-config', data)
}

// 更新邮箱配置
export function updateEmailConfig(id: number, data: UpdateEmailConfigParams) {
  return request.put<any, { data: EmailConfig }>(`/admin/email-config/${id}`, data)
}

// 删除邮箱配置
export function deleteEmailConfig(id: number) {
  return request.delete(`/admin/email-config/${id}`)
}

// 激活邮箱配置
export function activateEmailConfig(id: number) {
  return request.post(`/admin/email-config/${id}/activate`)
}

// 测试邮箱配置
export function testEmailConfig(id: number, data?: TestEmailConfigParams) {
  return request.post(`/admin/email-config/${id}/test`, data)
}

// 获取验证码统计信息
export function getVerificationStats() {
  return request.get('/email-verification/stats')
}

// 清理过期验证码
export function cleanupExpiredCodes() {
  return request.post('/email-verification/cleanup')
}
