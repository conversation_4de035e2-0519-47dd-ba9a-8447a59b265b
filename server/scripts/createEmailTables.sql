-- Active: 1752025475794@@127.0.0.1@3306@pet_finder_db
-- 创建邮箱配置表
CREATE TABLE IF NOT EXISTS `email_config` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `smtp_host` VARCHAR(255) NOT NULL COMMENT 'SMTP服务器地址',
  `smtp_port` INT NOT NULL DEFAULT 587 COMMENT 'SMTP端口',
  `smtp_secure` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否使用安全连接',
  `smtp_user` VARCHAR(255) NOT NULL COMMENT 'SMTP用户名（邮箱地址）',
  `smtp_pass` VARCHAR(255) NOT NULL COMMENT 'SMTP密码或授权码',
  `from_name` VARCHAR(100) NOT NULL COMMENT '发件人名称',
  `from_email` VARCHAR(255) NOT NULL COMMENT '发件人邮箱',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT = '邮箱配置表';

-- 创建邮箱验证码表
CREATE TABLE IF NOT EXISTS `email_verification_codes` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '验证码ID',
  `email` VARCHAR(255) NOT NULL COMMENT '邮箱地址',
  `code` VARCHAR(10) NOT NULL COMMENT '验证码',
  `type` ENUM('register', 'reset_password') NOT NULL DEFAULT 'register' COMMENT '验证码类型',
  `expires_at` TIMESTAMP NOT NULL COMMENT '过期时间',
  `is_used` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已使用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_email_type` (`email`, `type`),
  INDEX `idx_expires_at` (`expires_at`)
) ENGINE = InnoDB COMMENT = '邮箱验证码表';

-- 插入默认的163邮箱配置（需要管理员后续配置具体的邮箱和密码）
INSERT INTO `email_config` (
  `smtp_host`, 
  `smtp_port`, 
  `smtp_secure`, 
  `smtp_user`, 
  `smtp_pass`, 
  `from_name`, 
  `from_email`, 
  `is_active`
) VALUES (
  'smtp.163.com',
  587,
  TRUE,
  '<EMAIL>',
  'your_auth_code',
  '走失宠物协寻平台',
  '<EMAIL>',
  FALSE
) ON DUPLICATE KEY UPDATE id=id;
