import express from 'express';
import {
  getEmailConfigs,
  getEmailConfigById,
  createEmailConfig,
  updateEmailConfig,
  deleteEmailConfig,
  activateEmailConfig,
  testEmailConfig,
  getActiveEmailConfig
} from '../controllers/emailConfigController.js';
import { authenticateAdmin } from '../utils/jwt.js';
import { validateRequest } from '../utils/validation.js';
import Joi from 'joi';

const router = express.Router();

// 邮箱配置验证规则
const emailConfigSchema = Joi.object({
  smtp_host: Joi.string()
    .required()
    .messages({
      'any.required': 'SMTP服务器地址是必填项'
    }),
  
  smtp_port: Joi.number()
    .integer()
    .min(1)
    .max(65535)
    .required()
    .messages({
      'number.base': 'SMTP端口必须是数字',
      'number.integer': 'SMTP端口必须是整数',
      'number.min': 'SMTP端口必须大于0',
      'number.max': 'SMTP端口必须小于65536',
      'any.required': 'SMTP端口是必填项'
    }),
  
  smtp_secure: Joi.boolean()
    .default(true)
    .messages({
      'boolean.base': '安全连接设置必须是布尔值'
    }),
  
  smtp_user: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': 'SMTP用户名是必填项'
    }),
  
  smtp_pass: Joi.string()
    .required()
    .messages({
      'any.required': 'SMTP密码是必填项'
    }),
  
  from_name: Joi.string()
    .max(100)
    .required()
    .messages({
      'string.max': '发件人名称最多100个字符',
      'any.required': '发件人名称是必填项'
    }),
  
  from_email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '请输入有效的发件人邮箱地址',
      'any.required': '发件人邮箱是必填项'
    })
});

// 邮箱配置更新验证规则（所有字段都是可选的）
const emailConfigUpdateSchema = Joi.object({
  smtp_host: Joi.string().optional(),
  smtp_port: Joi.number().integer().min(1).max(65535).optional(),
  smtp_secure: Joi.boolean().optional(),
  smtp_user: Joi.string().email().optional(),
  smtp_pass: Joi.string().optional(),
  from_name: Joi.string().max(100).optional(),
  from_email: Joi.string().email().optional()
});

// 测试邮箱验证规则
const testEmailSchema = Joi.object({
  test_email: Joi.string()
    .email()
    .optional()
    .messages({
      'string.email': '请输入有效的测试邮箱地址'
    })
});

// 验证中间件
const createEmailConfigValidation = validateRequest(emailConfigSchema, 'body');
const updateEmailConfigValidation = validateRequest(emailConfigUpdateSchema, 'body');
const testEmailValidation = validateRequest(testEmailSchema, 'body');

/**
 * @route GET /api/admin/email-config
 * @desc 获取所有邮箱配置
 * @access Private (Admin)
 */
router.get('/', authenticateAdmin, getEmailConfigs);

/**
 * @route GET /api/admin/email-config/active
 * @desc 获取当前激活的邮箱配置
 * @access Private (Admin)
 */
router.get('/active', authenticateAdmin, getActiveEmailConfig);

/**
 * @route GET /api/admin/email-config/:id
 * @desc 根据ID获取邮箱配置
 * @access Private (Admin)
 */
router.get('/:id', authenticateAdmin, getEmailConfigById);

/**
 * @route POST /api/admin/email-config
 * @desc 创建邮箱配置
 * @access Private (Admin)
 */
router.post('/', authenticateAdmin, createEmailConfigValidation, createEmailConfig);

/**
 * @route PUT /api/admin/email-config/:id
 * @desc 更新邮箱配置
 * @access Private (Admin)
 */
router.put('/:id', authenticateAdmin, updateEmailConfigValidation, updateEmailConfig);

/**
 * @route DELETE /api/admin/email-config/:id
 * @desc 删除邮箱配置
 * @access Private (Admin)
 */
router.delete('/:id', authenticateAdmin, deleteEmailConfig);

/**
 * @route POST /api/admin/email-config/:id/activate
 * @desc 激活邮箱配置
 * @access Private (Admin)
 */
router.post('/:id/activate', authenticateAdmin, activateEmailConfig);

/**
 * @route POST /api/admin/email-config/:id/test
 * @desc 测试邮箱配置
 * @access Private (Admin)
 */
router.post('/:id/test', authenticateAdmin, testEmailValidation, testEmailConfig);

export default router;
